console.log('🚀 PASO 1: Test de Conexión Básica al Zebra FX9600');
console.log('================================================');

try {
    console.log('📦 Cargando librería llrpjs...');
    const llrpjs = require('llrpjs');
    console.log('✅ llrpjs cargado correctamente');
    
    console.log('🔗 Creando cliente LLRP...');
    const client = new llrpjs.LLRPClient({
        host: '*************',
        port: 5084
    });
    console.log('✅ Cliente creado');
    
    console.log('📡 Intentando conectar al reader...');
    
    let connected = false;
    
    client.on('connect', function() {
        console.log('🎉 ¡CONECTADO AL ZEBRA FX9600!');
        connected = true;
        
        setTimeout(() => {
            console.log('🔌 Desconectando...');
            client.disconnect();
        }, 2000);
    });
    
    client.on('error', function(error) {
        console.log('❌ ERROR DE CONEXIÓN:', error.message);
        console.log('   - Verifica que el reader esté encendido');
        console.log('   - Verifica la IP: *************');
        console.log('   - Verifica que el puerto 5084 esté abierto');
        process.exit(1);
    });
    
    client.on('disconnect', function() {
        if (connected) {
            console.log('✅ DESCONECTADO CORRECTAMENTE');
            console.log('🎯 PASO 1 COMPLETADO: Conexión básica funciona');
            process.exit(0);
        }
    });
    
    // Timeout de seguridad
    setTimeout(() => {
        if (!connected) {
            console.log('⏰ TIMEOUT: No se pudo conectar en 10 segundos');
            console.log('   - ¿Está encendido el reader?');
            console.log('   - ¿Es correcta la IP *************?');
            process.exit(1);
        }
    }, 10000);
    
    client.connect();
    
} catch (error) {
    console.log('❌ ERROR FATAL:', error.message);
    console.log(error.stack);
    process.exit(1);
}
